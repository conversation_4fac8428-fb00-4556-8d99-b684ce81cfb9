# 批量目标创建API使用说明

## 概述

重构后的 `batchCreateTargets` 方法支持按数据类型分组创建目标，并根据航迹名称前缀匹配相应的航迹。

## 主要改进

### 1. 支持数据类型分组
- **4**: 无人机 (UAV)
- **5**: 无人艇 (BOAT)
- **6**: 无人车 (CAR)
- **7**: 弹 (MISSILE)

### 2. 航迹和目标名称匹配
- **航迹匹配**：支持根据航迹名称前缀进行模糊查询
- **目标匹配**：支持根据目标名称前缀进行模糊查询（可选）
- 每种数据类型可以配置不同的航迹和目标名称前缀
- 例如：`"无人机航迹"` 可以匹配 `"无人机航迹1"`、`"无人机航迹2"` 等
- 例如：`"无人机"` 可以匹配 `"无人机-001"`、`"测试无人机"` 等

### 3. 灵活的配置方式
- 可以配置多种数据类型的混合目标
- 每种类型可以指定不同的数量
- 支持单一类型或混合类型的目标创建
- **每种目标类型可以独立配置任务和航迹参数**

## API接口

### 基础接口

**POST** `/api/batch-requirement/create-targets`

### 示例接口

1. **POST** `/api/batch-requirement/create-mixed-example` - 创建混合类型目标示例
2. **POST** `/api/batch-requirement/create-uav-example` - 创建无人机专项目标示例
3. **POST** `/api/batch-requirement/create-large-scale-example` - 创建大规模目标示例
4. **GET** `/api/batch-requirement/config-examples` - 获取配置示例

## 请求参数结构

```json
{
  "requirementName": "需求名称",
  "requirementType": 1,
  "importance": 2,
  "startTime": "2024-01-01T10:00:00",
  "endTime": "2024-01-02T10:00:00",
  "requirementComment": "需求描述",
  "targetCount": 70,
  "targetTypeConfigs": [
    {
      "dataType": 4,
      "count": 30,
      "trackNamePrefix": "无人机航迹",
      "targetNamePrefix": "无人机",
      "taskConfig": {
        "taskTypes": [1, 2, 3, 4],
        "taskStartOffset": 15,
        "taskDuration": 180,
        "repeatType": 0
      },
      "trackConfig": {
        "trackStartOffset": 0,
        "useExistingTrack": true
      }
    },
    {
      "dataType": 5,
      "count": 20,
      "trackNamePrefix": "无人艇航迹",
      "targetNamePrefix": "无人艇",
      "taskConfig": {
        "taskTypes": [1, 2],
        "taskStartOffset": 30,
        "taskDuration": 120,
        "repeatType": 0
      },
      "trackConfig": {
        "trackStartOffset": 10,
        "useExistingTrack": true
      }
    },
    {
      "dataType": 6,
      "count": 15,
      "trackNamePrefix": "无人车航迹",
      "targetNamePrefix": "无人车",
      "taskConfig": {
        "taskTypes": [1, 3],
        "taskStartOffset": 45,
        "taskDuration": 90,
        "repeatType": 0
      },
      "trackConfig": {
        "trackStartOffset": 20,
        "useExistingTrack": true
      }
    },
    {
      "dataType": 7,
      "count": 5,
      "trackNamePrefix": "弹航迹",
      "targetNamePrefix": "弹",
      "taskConfig": {
        "taskTypes": [2, 3],
        "taskStartOffset": 60,
        "taskDuration": 60,
        "repeatType": 0
      },
      "trackConfig": {
        "trackStartOffset": 30,
        "useExistingTrack": true
      }
    }
  ]
}
```

## 使用示例

### 示例1：混合类型目标（70个目标）

```bash
curl -X POST "http://localhost:8080/api/batch-requirement/create-mixed-example" \
  -H "Content-Type: application/json"
```

这将创建：
- 30个无人机目标，匹配"无人机航迹"前缀的航迹，任务延迟15分钟开始，持续3小时
- 20个无人艇目标，匹配"无人艇航迹"前缀的航迹，任务延迟30分钟开始，持续2小时，航迹延迟10分钟开始
- 15个无人车目标，匹配"无人车航迹"前缀的航迹，任务延迟45分钟开始，持续1.5小时，航迹延迟20分钟开始
- 5个弹目标，匹配"弹航迹"前缀的航迹，任务延迟1小时开始，持续1小时，航迹延迟30分钟开始

### 示例2：无人机专项目标（100个目标）

```bash
curl -X POST "http://localhost:8080/api/batch-requirement/create-uav-example" \
  -H "Content-Type: application/json"
```

这将创建100个无人机目标，匹配包含"无人机"的航迹名称。

### 示例3：大规模目标（1000个目标）

```bash
curl -X POST "http://localhost:8080/api/batch-requirement/create-large-scale-example" \
  -H "Content-Type: application/json"
```

这将创建：
- 500个无人机目标
- 300个无人艇目标
- 150个无人车目标
- 50个弹目标

### 示例4：自定义配置

```bash
curl -X POST "http://localhost:8080/api/batch-requirement/create-targets" \
  -H "Content-Type: application/json" \
  -d '{
    "requirementName": "自定义混合目标需求",
    "requirementType": 1,
    "importance": 1,
    "startTime": "2024-01-01T10:00:00",
    "endTime": "2024-01-02T10:00:00",
    "requirementComment": "自定义配置的混合目标",
    "targetCount": 50,
    "targetTypeConfigs": [
      {
        "dataType": 4,
        "count": 25,
        "trackNamePrefix": "测试无人机",
        "taskConfig": {
          "taskTypes": [1, 2, 4],
          "taskStartOffset": 0,
          "taskDuration": 90,
          "repeatType": 0
        },
        "trackConfig": {
          "trackStartOffset": 0,
          "useExistingTrack": true
        }
      },
      {
        "dataType": 6,
        "count": 25,
        "trackNamePrefix": "测试无人车",
        "taskConfig": {
          "taskTypes": [1, 3],
          "taskStartOffset": 30,
          "taskDuration": 60,
          "repeatType": 0
        },
        "trackConfig": {
          "trackStartOffset": 15,
          "useExistingTrack": true
        }
      }
    ]
  }'
```

## 数据验证

系统会自动验证：

1. **数据类型有效性**：只支持4、5、6、7这四种数据类型
2. **数量一致性**：`targetTypeConfigs` 中各类型数量之和必须等于 `targetCount`
3. **必填字段**：所有必填字段都不能为空
4. **数据库数据充足性**：确保数据库中有足够的目标和航迹数据

## 错误处理

常见错误及解决方案：

1. **"目标类型配置不能为空"**：需要提供 `targetTypeConfigs` 配置
2. **"数量不匹配"**：检查各类型数量之和是否等于总数量
3. **"不支持的数据类型"**：只能使用4、5、6、7这四种数据类型
4. **"目标数量不足"**：数据库中对应类型的目标数据不够
5. **"航迹数量不足"**：匹配指定前缀的航迹数据不够

## 注意事项

1. 确保数据库中有足够的目标数据和航迹数据
2. 航迹名称前缀要与数据库中的航迹名称匹配
3. 建议先使用示例接口测试功能
4. 大规模创建时注意系统性能和数据库连接
5. **每种目标类型都需要配置独立的任务和航迹参数**
6. **任务开始时间和航迹开始时间都是相对于需求开始时间的偏移量（分钟）**
7. **不同目标类型可以有不同的任务类型组合和时间安排**
