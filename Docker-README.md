# WRPT项目Docker部署指南

本文档介绍如何使用Docker部署WRPT后端项目。

## 文件说明

- `Dockerfile` - Docker镜像构建文件（仅运行时环境）
- `docker-compose.yml` - Docker Compose服务编排文件
- `.dockerignore` - Docker构建忽略文件
- `application-docker.yml` - Docker环境配置文件
- `build-and-run.sh` - Linux/Mac构建运行脚本
- `build-and-run.bat` - Windows构建运行脚本

## 构建流程说明

本Docker方案采用**分离构建**模式：
1. **Maven构建阶段**：在宿主机上使用Maven构建项目，生成jar包
2. **Docker镜像构建阶段**：将构建好的jar包打包到运行时镜像中

这种方式的优势：
- 构建速度更快（不需要在容器中下载依赖）
- 镜像体积更小（使用JRE而非JDK）
- 构建过程更透明（可以在宿主机上调试构建问题）

## 前置要求

- Java 8+
- Maven 3.6+
- Docker 20.10+
- Docker Compose 1.29+
- 至少2GB可用内存
- 至少5GB可用磁盘空间

## 快速开始

### 方式一：使用脚本（推荐）

#### Linux/Mac系统
```bash
# 给脚本执行权限
chmod +x build-and-run.sh

# 构建镜像
./build-and-run.sh build

# 启动服务
./build-and-run.sh start

# 查看状态
./build-and-run.sh status

# 查看日志
./build-and-run.sh logs
```

#### Windows系统
```cmd
# 构建镜像
build-and-run.bat build

# 启动服务
build-and-run.bat start

# 查看状态
build-and-run.bat status

# 查看日志
build-and-run.bat logs
```

### 方式二：手动执行

```bash
# 1. 安装本地依赖（如果存在）
mvn install:install-file \
    -Dfile=lib/orbitpre-orbit-1.0.0.jar \
    -DgroupId=com.cetc10.spaceflight \
    -DartifactId=orbitpre-orbit \
    -Dversion=1.0.0 \
    -Dpackaging=jar

# 2. Maven构建项目
mvn clean package -DskipTests

# 3. 构建Docker镜像
docker build -t wrpt-backend:latest .

# 4. 启动服务
docker-compose up -d

# 5. 查看服务状态
docker-compose ps

# 6. 查看日志
docker-compose logs -f wrpt-app
```

## 服务访问

启动成功后，可以通过以下地址访问服务：

- **应用主页**: http://localhost:8090/wrpt
- **Swagger API文档**: http://localhost:8090/wrpt/swagger-ui.html
- **MySQL数据库**: localhost:3306 (用户名: root, 密码: 12345)
- **Redis缓存**: localhost:6379

## 环境变量配置

可以通过修改`docker-compose.yml`中的环境变量来调整配置：

```yaml
environment:
  SPRING_PROFILES_ACTIVE: docker
  SPRING_DATASOURCE_URL: ****************************?...
  SPRING_DATASOURCE_USERNAME: root
  SPRING_DATASOURCE_PASSWORD: 12345
  SPRING_REDIS_HOST: redis
  SPRING_REDIS_PORT: 6379
  LOCAL_IP: wrpt-app
```

## 数据持久化

项目使用Docker卷来持久化数据：

- `mysql_data` - MySQL数据库数据
- `redis_data` - Redis缓存数据
- `./logs` - 应用日志文件
- `./files` - 应用文件存储

## 常用命令

```bash
# 查看服务状态
docker-compose ps

# 查看所有服务日志
docker-compose logs

# 查看特定服务日志
docker-compose logs wrpt-app
docker-compose logs mysql
docker-compose logs redis

# 重启服务
docker-compose restart

# 停止服务
docker-compose down

# 停止服务并删除数据卷
docker-compose down -v

# 进入容器
docker-compose exec wrpt-app sh
docker-compose exec mysql mysql -uroot -p12345
docker-compose exec redis redis-cli
```

## 故障排除

### 1. 端口冲突
如果遇到端口冲突，可以修改`docker-compose.yml`中的端口映射：
```yaml
ports:
  - "8091:8090"  # 将8090改为其他端口
```

### 2. 内存不足
如果遇到内存不足，可以调整JVM参数：
```yaml
environment:
  JAVA_OPTS: "-Xms256m -Xmx512m"
```

### 3. 数据库连接失败
确保MySQL服务已启动并等待几秒钟让数据库完全初始化：
```bash
docker-compose logs mysql
```

### 4. Maven构建失败
如果Maven构建失败，请检查：
```bash
# 检查Java版本
java -version

# 检查Maven版本
mvn -version

# 清理Maven缓存
mvn dependency:purge-local-repository
```

### 5. 本地依赖问题
如果构建时遇到本地依赖问题：
```bash
# 确保jar文件存在
ls -la lib/orbitpre-orbit-1.0.0.jar

# 手动安装依赖
mvn install:install-file \
    -Dfile=lib/orbitpre-orbit-1.0.0.jar \
    -DgroupId=com.cetc10.spaceflight \
    -DartifactId=orbitpre-orbit \
    -Dversion=1.0.0 \
    -Dpackaging=jar
```

### 6. jar包不存在
如果提示找不到`target/wrpt-1.0.jar`：
```bash
# 确保先执行Maven构建
mvn clean package -DskipTests

# 检查生成的jar包
ls -la target/*.jar
```

## 生产环境部署

生产环境部署时建议：

1. 修改默认密码
2. 使用外部数据库和Redis
3. 配置SSL证书
4. 设置适当的资源限制
5. 配置日志轮转
6. 设置健康检查

## 清理资源

完全清理所有Docker资源：
```bash
# 使用脚本
./build-and-run.sh cleanup

# 或手动执行
docker-compose down -v
docker rmi wrpt-backend:latest
```
