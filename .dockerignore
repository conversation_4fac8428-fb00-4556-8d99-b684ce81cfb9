# Git相关
.git
.gitignore
.gitattributes

# IDE相关
.idea/
*.iml
.vscode/
.eclipse/

# 源代码（只需要构建好的jar包）
src/
pom.xml
lib/

# 其他构建产物（保留target/wrpt-1.0.jar）
target/*
!target/wrpt-1.0.jar

# 日志文件
logs/
*.log
*.log.*

# 临时文件
*.tmp
*.temp
*.swp
*.swo
*~

# 操作系统相关
.DS_Store
Thumbs.db

# 文档
README.md
*.md
docs/

# Docker相关
Dockerfile
.dockerignore
docker-compose.yml

# 测试相关
.coverage
coverage/
test-results/

# Node.js相关（如果有前端构建）
node_modules/
npm-debug.log
yarn-error.log

# Maven相关
.mvn/
mvnw
mvnw.cmd
