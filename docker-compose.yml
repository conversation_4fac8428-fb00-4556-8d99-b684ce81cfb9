version: '3.8'

services:
  # MySQL数据库
  mysql:
    image: mysql:8.0
    container_name: wrpt-mysql
    environment:
      MYSQL_ROOT_PASSWORD: 12345
      MYSQL_DATABASE: wrpt
      MYSQL_USER: wrpt
      MYSQL_PASSWORD: 12345
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql:ro
    command: --default-authentication-plugin=mysql_native_password
    networks:
      - wrpt-network
    restart: unless-stopped

  # Redis缓存
  redis:
    image: redis:6.2-alpine
    container_name: wrpt-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - wrpt-network
    restart: unless-stopped

  # 应用服务
  wrpt-app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: wrpt-app
    environment:
      SPRING_PROFILES_ACTIVE: docker
      SPRING_DATASOURCE_URL: **********************************************************************************************************************************************************
      SPRING_DATASOURCE_USERNAME: root
      SPRING_DATASOURCE_PASSWORD: 12345
      SPRING_REDIS_HOST: redis
      SPRING_REDIS_PORT: 6379
      LOCAL_IP: wrpt-app
    ports:
      - "8090:8090"
    volumes:
      - ./logs:/app/logs
      - ./files:/app/files
    depends_on:
      - mysql
      - redis
    networks:
      - wrpt-network
    restart: unless-stopped

volumes:
  mysql_data:
  redis_data:

networks:
  wrpt-network:
    driver: bridge
