@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

echo === WRPT项目Docker构建和运行脚本 ===
echo.

if "%1"=="" goto :help
if "%1"=="help" goto :help
if "%1"=="build" goto :build
if "%1"=="start" goto :start
if "%1"=="stop" goto :stop
if "%1"=="restart" goto :restart
if "%1"=="status" goto :status
if "%1"=="logs" goto :logs
if "%1"=="cleanup" goto :cleanup
goto :help

:check_docker
echo [INFO] 检查Docker环境...
docker --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Docker未安装，请先安装Docker
    exit /b 1
)
docker-compose --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Docker Compose未安装，请先安装Docker Compose
    exit /b 1
)
echo [INFO] Docker环境检查通过
goto :eof

:check_maven
echo [INFO] 检查Maven环境...
mvn --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Maven未安装，请先安装Maven
    exit /b 1
)
echo [INFO] Maven环境检查通过
goto :eof

:install_local_deps
echo [INFO] 安装本地依赖...
if exist "lib\orbitpre-orbit-1.0.0.jar" (
    mvn install:install-file -Dfile=lib\orbitpre-orbit-1.0.0.jar -DgroupId=com.cetc10.spaceflight -DartifactId=orbitpre-orbit -Dversion=1.0.0 -Dpackaging=jar
    echo [INFO] 本地依赖安装完成
) else (
    echo [WARN] 本地依赖文件不存在: lib\orbitpre-orbit-1.0.0.jar
)
goto :eof

:build_project
echo [INFO] 开始Maven构建项目...
mvn clean package -DskipTests
if errorlevel 1 (
    echo [ERROR] Maven构建失败
    exit /b 1
)
echo [INFO] Maven构建成功
goto :eof

:create_directories
echo [INFO] 创建必要的目录...
if not exist "logs" mkdir logs
if not exist "files" mkdir files
echo [INFO] 目录创建完成
goto :eof

:build
call :check_docker
if errorlevel 1 exit /b 1
call :check_maven
if errorlevel 1 exit /b 1
call :create_directories
call :install_local_deps
call :build_project
if errorlevel 1 exit /b 1
echo [INFO] 开始构建Docker镜像...
docker build -t wrpt-backend:latest .
if errorlevel 1 (
    echo [ERROR] Docker镜像构建失败
    exit /b 1
)
echo [INFO] Docker镜像构建成功
goto :eof

:start
call :check_docker
if errorlevel 1 exit /b 1
call :create_directories
echo [INFO] 启动服务...
docker-compose up -d
if errorlevel 1 (
    echo [ERROR] 服务启动失败
    exit /b 1
)
echo [INFO] 服务启动成功
echo [INFO] 应用访问地址: http://localhost:8090/wrpt
echo [INFO] Swagger文档: http://localhost:8090/wrpt/swagger-ui.html
goto :eof

:stop
echo [INFO] 停止服务...
docker-compose down
echo [INFO] 服务已停止
goto :eof

:restart
call :stop
timeout /t 2 /nobreak >nul
call :start
goto :eof

:status
echo [INFO] 服务状态:
docker-compose ps
goto :eof

:logs
echo [INFO] 查看应用日志:
docker-compose logs -f wrpt-app
goto :eof

:cleanup
echo [WARN] 清理Docker资源...
docker-compose down -v
docker rmi wrpt-backend:latest 2>nul
echo [INFO] 清理完成
goto :eof

:help
echo 用法: %0 [选项]
echo.
echo 选项:
echo   build     构建Docker镜像
echo   start     启动所有服务
echo   stop      停止所有服务
echo   restart   重启所有服务
echo   status    查看服务状态
echo   logs      查看应用日志
echo   cleanup   清理所有资源
echo   help      显示此帮助信息
echo.
echo 示例:
echo   %0 build     # 构建镜像
echo   %0 start     # 启动服务
echo   %0 logs      # 查看日志
goto :eof
