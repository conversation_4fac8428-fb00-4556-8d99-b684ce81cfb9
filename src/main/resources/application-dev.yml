# ---------------------------- 数据库配置 ---------------------------------- #
spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: *******************************************************************************************************************************************************************
#    url: ***************************************************************************************************************************************************************
    username: root
    password: Infomix
    hikari:
      connection-test-query: SELECT 1 FROM DUAL
      connection-timeout: 6000000000
      maximum-pool-size: 500
      max-lifetime: 1800000
      minimum-idle: 20
      validation-timeout: 3000
      idle-timeout: 60000
    redis:
      host: 127.0.0.1
      port: 6379
      password:
  mybatis:
    mapper-locations: classpath:mapper/*.xml

# ---------------------------- mybatisPlus配置 ---------------------------- #
mybatis-plus:
  mapper-locations: classpath*:/mapper/*.xml
  #  configuration:
  #    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      insert-strategy: not_null
      update-strategy: not_null
      id-type: id_worker_str

logging:
  level:
    com.gy.show: debug
schedule:
  fixed-rate: 360000 # 一个小时，单位秒
# ---------------------------- 系统参数配置 ---------------------------- #

file:
  path: D:\ZQY\KY\file\
#  path: D:\zyf\temp\wrpt\

template:
  version: 1.0

# 目标与实际目标进行映射
target:
  mapping: "{1: '123', 2: '123', 3: '123', 4: '123'}"

# 本机IP地址
local:
  ip: auto
#  ip: **************

# 操控端管控中心UDP地址
controller:
  car:
    open: true
    id: _c1,_c2
    ter: 3,4
    # 无人车接收端
    receive:
      host: **********,**********
      port: 8001,8000
    # 无人车发送端
    send:
      host: **********,**********
      port: 7000,7000
  ship:
    open: true
    id: _ship
    ter: 1
    # 无人艇接收端
    receive:
      host: **********
      port: 10001
    # 无人艇发送端
    send:
      host: **********
      port: 20051
  uav:
    open: true
    id: _uav
    ter: 2
    # 无人机接收端
    receive:
      host: **********
      port: 10002
    # 无人机发送端
    send:
      host: **********
      port: 20061

# 终端UDP地址
terminal:
  open: true
  receive:
    host: ***********
    port: 5001
  send:
    host: ***********
    port: 4001

# 测控站地址
station:
  # 无人机UDP地址
  uav:
    open: true
    id: _uav
    receive:
      host: *********
      port: 6640
    send:
      host: ***********
      port: 6640
  # 无人机遥测UDP地址
  uav1:
    open: true
    id: _uav1
    receive:
      host: ***********
      port: 6210
  # 导弹UDP地址
  missile:
    open: true
    id: _missile
    receive:
      host: *********
      port: 14001
    send:
      host: ***********
      port: 30021
  # 航天测控站地址
  space:
    # 遥测数据
    open: true
    id: _ka,_s
    receive:
      host: *********02,***********
      port: 30003,30007
    # TCP 链接
    send:
      host: ***********,***********
      port: 5050,30006
  space1:
    # 状态数据
    open: true
    id: _ka,_s
    receive:
      host: *********01,***********
      port: 30002,30004
    send:
      host: *********00,***********
      port: 30002,30004
