package com.gy.show.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.gy.show.config.ExternalProperties;
import com.gy.show.constants.CacheConstant;
import com.gy.show.controller.external.StationLogDTO;
import com.gy.show.entity.dos.*;
import com.gy.show.entity.dto.WsMessageDTO;
import com.gy.show.enums.ClientTypeEnum;
import com.gy.show.enums.TargetMappingEnum;
import com.gy.show.mapper.CommonMapper;
import com.gy.show.service.*;
import com.gy.show.util.NodeSelection;
import com.gy.show.util.RedisUtil;
import com.gy.show.ws.FullViewTsServer;
import io.netty.buffer.ByteBuf;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.gy.show.constants.CacheConstant.STATION_REAL_DATA;

@Slf4j
public class StationServiceImpl {

    @Autowired
    SysDataMappingService dataMappingService;

    @Autowired
    DataGeneralService dataGeneralService;

    @Autowired
    CommonMapper commonMapper;

    @Autowired
    FullViewTsServer server;

    @Autowired
    private FileService fileService;

    @Autowired
    private SysDictionaryService dictionaryService;

    @Autowired
    private DataEquipmentOccupancyService dataEquipmentOccupancyService;

    @Autowired
    private RequirementTaskService requirementTaskService;

    @Autowired
    private TaskTargetRelationService taskTargetRelationService;

    @Autowired
    ExternalDataService externalDataService;

    @Autowired
    private ExternalProperties p;

    @Autowired
    private SysDataMappingService sysDataMappingService;

    public void sendBusinessData(byte[] data, String id, Integer taskId) {
        try {
            doSendBusinessData(data, id, taskId);
        } catch (Exception e) {
            log.error("转发遥测数据发生异常，启用默认遥测数据转发策略", e);
//            defaultDispatcherData(data, id);
        }

    }

    private void defaultDispatcherData(byte[] data, String id) {
        // 发送无人车
        ExternalProperties.Controller.Car car = p.getController().getCar();
        for (int i = 0; i < car.getId().size(); i++) {
            externalDataService.sendBusinessData(data, ClientTypeEnum.CAR.getMessage() + car.getId().get(i));
        }

        // 发送无人艇
        externalDataService.sendBusinessData(data, ClientTypeEnum.SHIP.getMessage() + id);

        // 发送无人机
        externalDataService.sendBusinessData(data, ClientTypeEnum.PLANE.getMessage() + id);

    }

    private void doSendBusinessData(byte[] data, String id, Integer taskId) {
        if (taskId != null) {
            // Ka S 走此逻辑
            TargetMappingEnum mappingEnum = TargetMappingEnum.getEnumByCode(taskId);

            /** 需要特殊处理ship2的逻辑
             * 1、只接收航天S的遥测数据
             * 2、目标4需要转发两份遥测数据
             * **/
            if (id.equals("_s") && taskId == 119) {
                log.info("根据航天测控站当前接入目标:{},额外转发一份遥测数据给：无人艇2", taskId);
                // 发送数据
                externalDataService.sendBusinessData(data, "ship_ship2");
            }

            log.info("根据航天测控站当前接入目标:{},转发遥测数据给：{}", taskId, mappingEnum.getFlag());
            // 发送数据
            externalDataService.sendBusinessData(data, mappingEnum.getFlag());
        } else {
            // DD UAV 测控站
            // 获取当前正在上报的站信息
            String stationInfo = RedisUtil.StringOps.get(CacheConstant.STATION_REAL_TIME + id);
            JSONObject station = JSON.parseObject(stationInfo);

            // 查询映射表
            List<SysDataMapping> dataMappings = sysDataMappingService.queryDataMapping(Arrays.asList(1, 6));

            // 获取当前节点对应终端（操控端）
            Integer curTer = getCurTer(station);

            if (curTer != 0) {
                SysDataMapping terMapping = dataMappings.stream()
                        .filter(f -> f.getDataType() == 1 && f.getDataKey().equals(curTer + ""))
                        .findAny().get();

                SysDataMapping targetMapping = dataMappings.stream()
                        .filter(f -> f.getDataType() == 6 && f.getDataValue().equals(terMapping.getDataValue()))
                        .findAny().get();

                // 发送数据
                externalDataService.sendBusinessData(data, targetMapping.getDataKey());

                // 如果不为空则有规划的任务，根据规划的任务去进行遥测数据转发
//                if (CollUtil.isNotEmpty(equipments)) {
                // 根据站点ID查询当前站点是否有预规划的任务
//                List<DataEquipmentOccupancy> equipments = dataEquipmentOccupancyService.list(Wrappers.<DataEquipmentOccupancy>lambdaQuery().eq(DataEquipmentOccupancy::getEquipmentId, station.getString("id"))
//                        .ge(DataEquipmentOccupancy::getStartTime, LocalDateTime.now())
//                        .le(DataEquipmentOccupancy::getEndTime, LocalDateTime.now())
//                        .orderByDesc(DataEquipmentOccupancy::getCreateTime));
                // 如果有多个则取最新创建的一个
//            RequirementTask task = requirementTaskService.getById(equipments.get(0).getTaskId());
//
//            // 查询任务目标关联表
//            TaskTargetRelation targetRelation = taskTargetRelationService.getById(task.getTargetRelationId());
//
//            List<SysDataMapping> mappings = dataMappings.stream()
//                    .filter(d -> d.getDataType() == 6 && d.getDataValue().equals(targetRelation.getTargetId()))
//                    .collect(Collectors.toList());
//
//            if (CollUtil.isNotEmpty(mappings)) {
//                SysDataMapping sysDataMapping = mappings.get(0);
//                // 发送数据
//                externalDataService.sendBusinessData(data, sysDataMapping.getDataKey());
//            }
//                } else {
//                    // 获取当前节点对应终端（操控端）
//                    Integer curTer = getCurTer(station);
//
//                    if (curTer != 0) {
//                        SysDataMapping terMapping = dataMappings.stream()
//                                .filter(f -> f.getDataType() == 1 && f.getDataKey().equals(curTer + ""))
//                                .findAny().get();
//
//                        SysDataMapping targetMapping = dataMappings.stream()
//                                .filter(f -> f.getDataType() == 6 && f.getDataValue().equals(terMapping.getDataValue()))
//                                .findAny().get();
//
//                        // 发送数据
//                        externalDataService.sendBusinessData(data, targetMapping.getDataKey());
//                    }
            }
        }
    }

    private Integer getCurTer(JSONObject station) {
        // 当前节点对应的ID
        String id = station.getString("id");

        Integer curTer = 0;
        for (int i = 1; i < 6; i++) {
            long terCurrentNode = NodeSelection.getTerCurrentNode(i);

            if (id.equals(terCurrentNode + "")) {
                curTer = i;
                break;
            }
        }

        return curTer;
    }

    public Map<String, Object> mappingStation(String id) {
        Map<String, Object> dataInfo;
        String info = RedisUtil.StringOps.get(CacheConstant.STATION_REAL_TIME + id);
        if (StringUtils.isBlank(info)) {
            // 查询映射关系表 2 表示航天站
            List<SysDataMapping> mapping = dataMappingService.list(Wrappers.<SysDataMapping>lambdaQuery().eq(SysDataMapping::getDataType, 2));

            Map<String, List<SysDataMapping>> keyMap = mapping.stream()
                    .collect(Collectors.groupingBy(SysDataMapping::getDataKey));

            SysDataMapping dataMapping = keyMap.get(id).get(0);

            String[] values = dataMapping.getDataValue().split("_");
            DataGeneral dataGeneral = dataGeneralService.getById(values[0]);

            // 获取图片
            String imgUrl = fileService.getImageByType(dataGeneral.getDataType());

            dataInfo = commonMapper.getOne(dataGeneral.getTableName(), values[1]);

            List<SysDictionary> sourceType = dictionaryService.getDicByType("sourceType");
            Optional<SysDictionary> first = sourceType.stream().filter(t -> t.getDictValue() == dataGeneral.getDataType()).findFirst();


            dataInfo.put("imgUrl", imgUrl);
            dataInfo.put("typeValue", dataGeneral.getTableComment());
            dataInfo.put("dataTypeValue", first.get().getDictName());
            dataInfo.put("dataType", dataGeneral.getDataType());

            // 存入缓存
            RedisUtil.StringOps.setEx(CacheConstant.STATION_REAL_TIME + id, JSON.toJSONString(dataInfo), 5, TimeUnit.SECONDS);
        } else {
            // 如果存在该缓存则刷新该缓存的过期时间，不作其它处理，后面会根据解析结果进行赋值
            RedisUtil.KeyOps.expire(CacheConstant.STATION_REAL_TIME + id, 5, TimeUnit.SECONDS);

            dataInfo = JSON.parseObject(info, Map.class);
        }

        return dataInfo;
    }
    public void sendMessage2Front(Object data, Integer type) { WsMessageDTO dto = new WsMessageDTO();
        dto.setData(data);
        dto.setType(type);

        server.sendAll(JSON.toJSONString(dto));
    }

    public void sendStationMessage2Front(ByteBuf byteBuf, String type, String message, String id) {
        // 标记当前读指针位置
        byteBuf.markReaderIndex();

        byte[] bytes = new byte[byteBuf.readableBytes()];
        byteBuf.readBytes(bytes);

        // 转换16进制字符串
        StringBuilder hexBuilder = new StringBuilder();
        for (byte b : bytes) {
            hexBuilder.append(String.format("%02X", b))
                    .append(" ");
        }

        // 重置读指针状态
        byteBuf.resetReaderIndex();

        StationLogDTO logDTO = new StationLogDTO();
        logDTO.setData(hexBuilder.toString());
        logDTO.setTime(DateUtil.now());
        logDTO.setType(type);
        logDTO.setMessage(message);
        Map<String, Object> stationData = getStationData(id);
        if (stationData != null) {
            logDTO.setId(stationData.get("id").toString());
        }

        // 向前端发送日志信息
        server.sendStationLog(JSON.toJSONString(logDTO));
    }

    public Map<String, Object> getStationData(String id) {
        String station = RedisUtil.StringOps.get(CacheConstant.STATION_REAL_TIME + id);

        Map<String, Object> stationMap = null;
        if (StringUtils.isNotBlank(station)) {
            stationMap = JSON.parseObject(station, new TypeReference<Map<String, Object>>(){});
        }

        return stationMap;
    }
    public void cacheStationData(Object result, String id) {
        List<Map<String, Object>> arr = (List<Map<String, Object>>) result;
        Object stationInfo = arr.get(0).get("stationInfo");
        Map<String, Object> stationJson = (Map<String, Object>) stationInfo;
        Object nodeId = stationJson.get("id");

        RedisUtil.StringOps.setEx(CacheConstant.STATION_INFO + nodeId, JSON.toJSONString(stationInfo), 30, TimeUnit.SECONDS);

        // 缓存站点数据
        RedisUtil.StringOps.setEx(STATION_REAL_DATA + id, JSON.toJSONString(result), 10, TimeUnit.SECONDS);
    }
}
