# 使用OpenJDK 8运行时镜像
FROM openjdk:8-jre-alpine

# 设置维护者信息
LABEL maintainer="wrpt-backend"

# 设置工作目录
WORKDIR /app

# 安装必要的工具并设置时区
RUN apk add --no-cache tzdata && \
    cp /usr/share/zoneinfo/Asia/Shanghai /etc/localtime && \
    echo "Asia/Shanghai" > /etc/timezone && \
    apk del tzdata

# 创建应用用户（安全最佳实践）
RUN addgroup -g 1000 appgroup && \
    adduser -u 1000 -G appgroup -s /bin/sh -D appuser

# 创建必要的目录
RUN mkdir -p /app/logs /app/files && \
    chown -R appuser:appgroup /app

# 复制构建好的jar包
COPY target/wrpt-1.0.jar app.jar

# 设置文件权限
RUN chown appuser:appgroup app.jar

# 切换到应用用户
USER appuser

# 暴露端口
EXPOSE 8090

# 设置JVM参数
ENV JAVA_OPTS="-Xms512m -Xmx1024m -Djava.security.egd=file:/dev/./urandom"

# 设置Spring Boot配置文件
ENV SPRING_PROFILES_ACTIVE=prod

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=60s --retries=3 \
    CMD wget --no-verbose --tries=1 --spider http://localhost:8090/wrpt/actuator/health || exit 1

# 启动应用
CMD ["sh", "-c", "java $JAVA_OPTS -jar app.jar --spring.profiles.active=$SPRING_PROFILES_ACTIVE"]
