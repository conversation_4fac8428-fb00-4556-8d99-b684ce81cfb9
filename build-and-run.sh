#!/bin/bash

# WRPT项目Docker构建和运行脚本

set -e

echo "=== WRPT项目Docker构建和运行脚本 ==="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Docker是否安装
check_docker() {
    if ! command -v docker &> /dev/null; then
        print_error "Docker未安装，请先安装Docker"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        print_error "Docker Compose未安装，请先安装Docker Compose"
        exit 1
    fi
    
    print_info "Docker环境检查通过"
}

# 创建必要的目录
create_directories() {
    print_info "创建必要的目录..."
    mkdir -p logs
    mkdir -p files
    print_info "目录创建完成"
}

# 检查Maven是否安装
check_maven() {
    if ! command -v mvn &> /dev/null; then
        print_error "Maven未安装，请先安装Maven"
        exit 1
    fi
    print_info "Maven环境检查通过"
}

# 安装本地依赖
install_local_deps() {
    print_info "安装本地依赖..."
    if [ -f "lib/orbitpre-orbit-1.0.0.jar" ]; then
        mvn install:install-file \
            -Dfile=lib/orbitpre-orbit-1.0.0.jar \
            -DgroupId=com.cetc10.spaceflight \
            -DartifactId=orbitpre-orbit \
            -Dversion=1.0.0 \
            -Dpackaging=jar
        print_info "本地依赖安装完成"
    else
        print_warn "本地依赖文件不存在: lib/orbitpre-orbit-1.0.0.jar"
    fi
}

# Maven构建项目
build_project() {
    print_info "开始Maven构建项目..."
    mvn clean package -DskipTests
    if [ $? -eq 0 ]; then
        print_info "Maven构建成功"
    else
        print_error "Maven构建失败"
        exit 1
    fi
}

# 构建Docker镜像
build_image() {
    print_info "开始构建Docker镜像..."
    docker build -t wrpt-backend:latest .
    if [ $? -eq 0 ]; then
        print_info "Docker镜像构建成功"
    else
        print_error "Docker镜像构建失败"
        exit 1
    fi
}

# 使用docker-compose启动服务
start_services() {
    print_info "启动服务..."
    docker-compose up -d
    if [ $? -eq 0 ]; then
        print_info "服务启动成功"
        print_info "应用访问地址: http://localhost:8090/wrpt"
        print_info "Swagger文档: http://localhost:8090/wrpt/swagger-ui.html"
    else
        print_error "服务启动失败"
        exit 1
    fi
}

# 停止服务
stop_services() {
    print_info "停止服务..."
    docker-compose down
    print_info "服务已停止"
}

# 查看服务状态
show_status() {
    print_info "服务状态:"
    docker-compose ps
}

# 查看日志
show_logs() {
    print_info "查看应用日志:"
    docker-compose logs -f wrpt-app
}

# 清理资源
cleanup() {
    print_warn "清理Docker资源..."
    docker-compose down -v
    docker rmi wrpt-backend:latest 2>/dev/null || true
    print_info "清理完成"
}

# 显示帮助信息
show_help() {
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  build     构建Docker镜像"
    echo "  start     启动所有服务"
    echo "  stop      停止所有服务"
    echo "  restart   重启所有服务"
    echo "  status    查看服务状态"
    echo "  logs      查看应用日志"
    echo "  cleanup   清理所有资源"
    echo "  help      显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 build     # 构建镜像"
    echo "  $0 start     # 启动服务"
    echo "  $0 logs      # 查看日志"
}

# 主函数
main() {
    case "${1:-help}" in
        "build")
            check_docker
            check_maven
            create_directories
            install_local_deps
            build_project
            build_image
            ;;
        "start")
            check_docker
            create_directories
            start_services
            ;;
        "stop")
            stop_services
            ;;
        "restart")
            stop_services
            sleep 2
            start_services
            ;;
        "status")
            show_status
            ;;
        "logs")
            show_logs
            ;;
        "cleanup")
            cleanup
            ;;
        "help"|*)
            show_help
            ;;
    esac
}

# 执行主函数
main "$@"
